"""
双向lstm
"""

import warnings

from torch.nn.utils import parameters_to_vector
from torch.optim import lr_scheduler
import numpy as np
import pickle
import matplotlib.pyplot as plt
import copy
# import matlab.engine
import pandas as pd
import os
import torch
from torch import nn
from torch.utils import data
from torch.utils.data import Dataset
from sklearn.preprocessing import MinMaxScaler
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error as RMSE
from sklearn.metrics import mean_absolute_error as MAE
from sklearn.metrics import r2_score
import scipy as sp
import time
from sklearn.preprocessing import StandardScaler
import torch.nn.functional as F
import random
import math
from sklearn.metrics import roc_curve, auc, accuracy_score, average_precision_score, confusion_matrix, f1_score,\
    precision_score, recall_score
from sklearn.model_selection import KFold, StratifiedKFold
from thop import profile





class My_net_lstm(nn.Module):

    def __init__(self, num_inputs, num_hiddens, num_outputs, num_layers=1, dropout=0.1, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)

        self.bgru1 = nn.GRU(num_inputs, num_hiddens, num_layers, dropout=dropout, bidirectional=True, batch_first=True)
        self.bgru2 = nn.GRU(num_hiddens*2, num_outputs, num_layers, dropout=dropout, bidirectional=True, batch_first=True)
        self.dropout = nn.Dropout(dropout)
        self.num_hiddens = num_hiddens
        self.num_layers = num_layers
        self.num_outputs = num_outputs

    def forward(self, X):
        h1 = torch.zeros((self.num_layers*2, X.shape[0], self.num_hiddens), device=device)
        h2 = torch.zeros((self.num_layers*2, X.shape[0], self.num_outputs), device=device)
        X = X.permute(0, 2, 1)
        output, state = self.bgru1(X, h1)
        output, state = self.bgru2(output, h2)
        output = self.dropout(state[-2:, :, :].permute(1, 0, 2).reshape(X.shape[0], -1))
        return output


def balanced_accuracy_score(y_true, y_pred, labels=None, *, sample_weight=None, adjusted=False):
    if labels is None:
        labels = [0, 1]
    C = confusion_matrix(y_true, y_pred, labels=labels, sample_weight=sample_weight)
    with np.errstate(divide="ignore", invalid="ignore"):
        per_class = np.diag(C) / C.sum(axis=1)
    if np.any(np.isnan(per_class)):
        warnings.warn("y_pred contains classes not in y_true")
        per_class = per_class[~np.isnan(per_class)]
    score = np.mean(per_class)
    if adjusted:
        n_classes = len(per_class)
        chance = 1 / n_classes
        score -= chance
        score /= 1 - chance
    return score


class net_combine(nn.Module):

    def __init__(self, net1, net2, output_channels, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.nets = nn.ModuleList([net1, net2])
        self.linear1 = nn.Linear(output_channels, 1)
        self.linear2 = nn.Linear(output_channels, 1)

    def forward(self, X):
        X1 = self.nets[0](X)
        X2 = self.nets[1](X)
        return self.linear1(X1), self.linear2(X2)


class TimeseriesDataset(Dataset):
    def __init__(self, data_dict, label_dict):
        self.data = torch.from_numpy(data_dict).unsqueeze(1)
        self.label = torch.from_numpy(label_dict).unsqueeze(2)
        # self.label2 = torch.from_numpy(label_dict2)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, index):
        X = self.data[index].float()
        Y1 = self.label[index][0].float()
        Y2 = self.label[index][1].float()
        return X, Y1, Y2

class CosineScheduler:
    """
    余弦衰减学习率调度器
    """
    def __init__(self, max_update, base_lr=0.1, final_lr=0.001, warmup_steps=0, warmup_begin_lr=0) -> None:
        self.max_update = max_update
        self.base_lr_orig = base_lr
        self.final_lr = final_lr
        self.warmup_steps = warmup_steps
        self.warmup_begin_lr = warmup_begin_lr
        self.max_steps = self.max_update - self.warmup_steps

    def get_warm_lr(self, epoch):
        """
        获取warmup的学习率
        :param epoch:
        :return:
        """
        increase = (self.base_lr_orig - self.warmup_begin_lr) * float(epoch) / float(self.warmup_steps)
        return self.warmup_begin_lr + increase

    def __call__(self, epoch):
        if epoch < self.warmup_steps:
            return self.get_warm_lr(epoch)
        if epoch <= self.max_update:
            self.base_lr = self.final_lr + 0.5 * (self.base_lr_orig - self.final_lr) * (1 +
                        math.cos(math.pi * (epoch - self.warmup_steps) / self.max_steps))
            return self.base_lr
        return self.final_lr


def seed_torch(seed=1029):
    random.seed(seed)   # Python的随机性
    os.environ['PYTHONHASHSEED'] = str(seed)    # 设置Python哈希种子，为了禁止hash随机化，使得实验可复现
    np.random.seed(seed)   # numpy的随机性
    torch.manual_seed(seed)   # torch的CPU随机性，为CPU设置随机种子
    torch.cuda.manual_seed(seed)   # torch的GPU随机性，为当前GPU设置随机种子
    torch.cuda.manual_seed_all(seed)  # if you are using multi-GPU.   torch的GPU随机性，为所有GPU设置随机种子
    torch.backends.cudnn.benchmark = False   # if benchmark=True, deterministic will be False
    torch.backends.cudnn.deterministic = True   # 选择确定性算法

def evaluate(net, test_iter, device, epoch):
    """
    推理模式，返回整个testset的RMSE和MAE
    :param net:
    :param test_iter:
    :param device:
    :return:
    """
    net.eval()
    loss = nn.MSELoss(reduction='sum')
    L=0
    kk=0
    score_list = []  # 存储预测得分
    label_list = []  # 存储真实标签
    rmse, num1, mae, num2, time_jia = 0, np.count_nonzero(test_Y[:, 0]), 0, np.count_nonzero(test_Y[:, 0]), 0
    with torch.no_grad():
        time_start = time.time()
        for batch in test_iter:
            X, Y1, Y2 = batch[0].to(device), batch[1].to(device), batch[2].to(device)
            y_hat1, y_hat2 = net(X)
            y_hat1 = torch.sigmoid(y_hat1)
            time_end = time.time()
            time_jia += (time_end-time_start)
            score_list.extend(y_hat1.detach().cpu().numpy())
            label_list.extend(Y1.cpu().numpy())
            rmse += torch.sum(torch.square(y_hat2-Y2)*Y1.int())
            # num1 += y_hat.shape[0]
            mae += torch.sum(torch.abs(y_hat2-Y2)*Y1.int())
            # num2 += y_hat.shape[0]
            # L += loss(y_hat2, Y2)*Y1.int()
            # kk += Y2.shape[0] * Y2.shape[1]
            time_start = time.time()
        score_label_list = [1 if i > 0.5 else 0 for i in score_list]
        score_label_array = np.array(score_label_list).squeeze()
        score_array = np.array(score_list).squeeze()
        # 将label转换成onehot形式
        label_array = np.array(label_list).squeeze()
        # fpr, tpr, thresholds = roc_curve(label_array, score_array)
        # auc_value = auc(fpr, tpr)
        accuracy = accuracy_score(label_array, score_label_array)
        balanced_accuracy = balanced_accuracy_score(label_array, score_label_array, labels=[1, 0])
        # average_precision = average_precision_score(label_array, score_array)
        f1 = f1_score(label_array, score_label_array, pos_label=0)
        precision = precision_score(label_array, score_label_array, pos_label=0)
        recall = recall_score(label_array, score_label_array, pos_label=0)
        # label_tensor = label_tensor.reshape((label_tensor.shape[0], 1))
        # label_onehot = torch.zeros(label_tensor.shape[0], 2)
        # label_onehot.scatter_(dim=1, index=label_tensor, value=1)
        # label_onehot = np.array(label_onehot)
        time_inference[index_i, epoch//5] = time_jia
    return torch.sqrt(rmse / num1), mae / num2, precision, accuracy, balanced_accuracy, recall, f1


def xavier_init_weights(m):
    """
    初始化网络的参数
    :param m:
    """
    if type(m) == nn.Linear or type(m) == nn.Conv1d:
        nn.init.xavier_uniform_(m.weight)
    if type(m) == nn.LSTM:
        for name, param in m.named_parameters():
            if name.startswith("weight"):
                nn.init.xavier_uniform_(param)


def train_model(net, train_iter, lr, num_epochs, device):
    """
    训练模式
    :param net:
    :param train_iter:
    :param lr:
    :param num_epochs:
    :param device:
    :return:
    """

    net.to(device)
    optimizer = torch.optim.Adam(net.parameters(), lr=lr)
    scheduler = CosineScheduler(num_epochs, lr, lr/10, 0, 0)
    # scheduler = None
    # optimizer = torch.optim.Adam(net.parameters(), lr=lr)
    # optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    loss2 = nn.MSELoss(reduction='none')
    loss1 = nn.BCEWithLogitsLoss(pos_weight=torch.tensor([0.25]))

    kk1 = train_Y.shape[0]
    kk2 = np.count_nonzero(train_Y[:, 0])
    for epoch in range(num_epochs):
        # print(epoch)


        net.train()
        # num_t = 0
        time_start = time.time()
        for batch in train_iter:

            optimizer.zero_grad()
            X, Y1, Y2 = batch[0].to(device), batch[1].to(device), batch[2].to(device)
            y_hat1, y_hat2 = net(X)  #64*7*1
            # flops, params = profile(net, inputs=(X,))
            l1 = loss1(y_hat1, Y1)
            l2 = (loss2(y_hat2, Y2) * Y1.int()).sum() / (torch.count_nonzero(Y1)+1e-8)
            # l3 = (parameters_to_vector(net.nets[1].parameters())-parameters_to_vector(net.nets[0].parameters())).norm(2).pow(2)
            # l = lamda1*l1 + lamda2*l2 + lamda3*l3
            l = lamda1 * l1 + lamda2 * l2
            l.backward()
            # grad_clipping(net, 1)
            optimizer.step()
            with torch.no_grad():
                loss_epochs_train[index_i, 0, epoch] += l1 * Y1.shape[0] * Y1.shape[1]
                loss_epochs_train[index_i, 1, epoch] += l2 * torch.count_nonzero(Y1)
        if scheduler:
            if scheduler.__module__ == lr_scheduler.__name__:
                scheduler.step()
            else:
                for param_group in optimizer.param_groups:
                    param_group['lr'] = scheduler(epoch+1)
        # time_end = time.time()
        # num_t += time_end-time_start
        # print(num_t)
        loss_epochs_train[index_i, 0, epoch] /= kk1
        loss_epochs_train[index_i, 0, epoch] = loss_epochs_train[index_i, 0, epoch]
        loss_epochs_train[index_i, 1, epoch] /= kk2
        loss_epochs_train[index_i, 1, epoch] = loss_epochs_train[index_i, 1, epoch]
        print('train epoch:{},rmse:{},'.format(epoch + 1, loss_epochs_train[index_i, 0, epoch]),
              'bce:{},'.format(loss_epochs_train[index_i, 1, epoch]))
        if (epoch + 1) % 5 == 0 or epoch + 1 == 1:
            # time_start = time.time()
            loss_epochs_test[index_i, 0, epoch//5], loss_epochs_test[index_i, 1, epoch//5], loss_epochs_test[index_i][2][epoch//5],\
                loss_epochs_test[index_i][3][epoch//5], loss_epochs_test[index_i][4][epoch//5], loss_epochs_test[index_i][5][epoch//5],\
                loss_epochs_test[index_i][6][epoch//5] = evaluate(net, test_iter, device, epoch)
            # time_end = time.time()
            # print(time_start - time_end)
            print('test epoch:{},rmse:{},'.format(epoch+1, loss_epochs_test[index_i, 0, epoch//5]),
                  'mae:{},'.format(loss_epochs_test[index_i, 1, epoch//5]),
                  'precision:{},'.format(loss_epochs_test[index_i][2][epoch//5]))
            print('accuracy:{},'.format(loss_epochs_test[index_i][3][epoch//5]),
                  'balanced_accuracy:{},'.format(loss_epochs_test[index_i][4][epoch//5]),
                  'recall:{},'.format(loss_epochs_test[index_i][5][epoch//5]),
                  'f1:{},'.format(loss_epochs_test[index_i][6][epoch//5]))
            if best_rmse[-1] > loss_epochs_test[index_i, 0, epoch//5] or best_mae[-1] > loss_epochs_test[index_i, 1, epoch//5]:
                if best_rmse[-1] > loss_epochs_test[index_i, 0, epoch//5]:
                    best_rmse.append(loss_epochs_test[index_i, 0, epoch//5])
                    rmse_epoch.append(epoch)
                if best_mae[-1] > loss_epochs_test[index_i, 1, epoch//5]:
                    best_mae.append(loss_epochs_test[index_i, 1, epoch//5])
                    mae_epoch.append(epoch)
                save_path1 = os.path.join(save_path, "model_fitune_epoch_{}.pt".format(epoch+1))
                torch.save({
                    'epoch': epoch+1,
                    'net_model_state_dict': net.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                }, save_path1)
                dict_netmodel = {'net': net.__str__(), 'batch_size': batch_size, 'learning rate': lr,
                                 'learning rate decay': lr_decay_label,
                                 'num_epoch': num_epochs, 'input normalized?': normalize_input_label,
                                 'best rmse': best_rmse[-1], 'best mae': best_mae[-1],'rmsenum_epoch': rmse_epoch[-1],
                                          'maenum_epoch': mae_epoch[-1], 'seed': seed,     'lamda2' : lamda2,
    'lamda3' : lamda3,
    }
                df = pd.DataFrame.from_dict(dict_netmodel, orient='index')
                df.to_csv(os.path.join(save_path, "net_model{}.csv".format(model_count)))
    plt.clf()
    plt.figure(1)
    plt.plot(np.arange(num_epochs)+1, loss_epochs_train[index_i][0], label='train loss mse')
    plt.plot(np.arange(num_epochs)+1, loss_epochs_train[index_i][1], label='train loss bce')
    # plt.plot(np.arange(num_epochs) + 1, loss_epochs_test[2], label='test loss')
    plt.legend()
    plt.show()
    plt.figure(2)
    plt.plot(np.arange(num_epochs//5) + 1, loss_epochs_test[index_i][0], label='test RMSE')
    plt.plot(np.arange(num_epochs//5) + 1, loss_epochs_test[index_i][1], label='test MAE')
    # plt.yscale("log")
    plt.legend()
    plt.show()


    # plt.legend()
    # plt.show()
    print(np.min(loss_epochs_train[-1]))
    # print(np.min(loss_epochs_test[0][-1]))


if __name__ == '__main__':
    seed = 555
    network_type = 'bgru'
    lr, batch_size, num_epochs, num_outputs, batch_size_eva = 0.00001, 4, 1000, 1, 4
    lr_decay_label, normalize_input_label, best_rmse, best_epoch, best_mae = True, True, [100], [0], [100]
    rmse_epoch = [0]
    mae_epoch = [0]
    model_count = 4
    # model_pre_count = 1
    save_path = './models_class_regression_5_20{}{}/'.format(network_type, model_count)
    train_test_rate = 0.2
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    if not os.path.exists(save_path):
        os.mkdir(save_path)

    seed_torch(seed)
    with open('result.pkl', 'rb') as f:
        result = pickle.load(f)
    data_x = []
    data_label_SOH = []
    data_label = []

    for i in range(len(result['异常']['V'])):
        data_t = result['异常']['t'][i]
        data_SOH = result['异常']['SOH'][i]
        data_x.append(data_t)
        data_label_SOH.append(np.array([0, data_SOH]))
        data_label.append(0)
    for i in range(len(result['正常']['V'])):
        data_t = result['正常']['t'][i]
        data_SOH = result['正常']['SOH'][i]
        data_x.append(data_t)
        data_label_SOH.append(np.array([1, data_SOH]))
        data_label.append(1)
    data_x = np.array(data_x)
    data_label_SOH = np.array(data_label_SOH)
    data_label = np.array(data_label)
    n_splits = 5
    skf = StratifiedKFold(n_splits=n_splits, shuffle=False)
    loss_epochs_test = np.zeros((n_splits, 7, num_epochs // 5))
    loss_epochs_train = np.zeros((n_splits, 2, num_epochs))
    time_inference = np.zeros((n_splits, num_epochs // 5))
    df_loss_epochs_test_all = pd.DataFrame()
    df_loss_epochs_train_all = pd.DataFrame()
    df_time_inference_all = pd.DataFrame()
    for index_i, (train_index, test_index) in enumerate(skf.split(data_x, data_label)):
        train_X, test_X = data_x[train_index], data_x[test_index]
        train_Y, test_Y = data_label_SOH[train_index], data_label_SOH[test_index]
    # train_X, test_X, train_Y, test_Y = train_test_split(data_x, data_label_SOH, test_size=train_test_rate, stratify=data_label)
        scaler_v = StandardScaler()
        scaler_v.fit(train_X.reshape(-1, 1))
        train_X = scaler_v.transform(train_X.reshape(-1, 1)).reshape(-1, len(train_X[1]))
        test_X = scaler_v.transform(test_X.reshape(-1, 1)).reshape(-1, len(train_X[1]))
    # with open('train_test_x_y.pkl', 'rb') as f:
    #     result = pickle.load(f)
    # train_X = result['train_X']
    # train_Y = result['train_Y']
    # test_X = result['test_X']
    # test_Y = result['test_Y']
    # for i in range(len(train_Y)):
    #     if train_Y[i, 0] == 0:
    #         train_Y[i, 0] = 1
    #     else:
    #         train_Y[i, 0] = 0
    # for i in range(len(test_Y)):
    #     if test_Y[i, 0] == 0:
    #         test_Y[i, 0] = 1
    #     else:
    #         test_Y[i, 0] = 0
        train_dataset = TimeseriesDataset(train_X, train_Y)
        test_dataset = TimeseriesDataset(test_X, test_Y)
        train_iter = data.DataLoader(train_dataset, batch_size, shuffle=True, pin_memory=True)
        test_iter = data.DataLoader(test_dataset, batch_size_eva, shuffle=False, pin_memory=True)
        net1 = My_net_lstm(1, 8, 16)
        net1.apply(xavier_init_weights)
        net2 = My_net_lstm(1, 8, 16)
        net2.apply(xavier_init_weights)
        # net2 = copy.deepcopy(net1)
        net = net_combine(net1,net2, 32)

        lamda1 = 0.1
        lamda2 = 0.1
        lamda3 = 0.0001


        # net = torch.compile(net)
        # X = torch.rand((1, 1, 30))  # batch_size, input_channel, seq_len
        # for layer in net:
        #     X = layer(X)
        #     print(layer.__class__.__name__, 'output shape:\t', X.shape)

        train_model(net, train_iter, lr, num_epochs, device)

        df_loss_epochs_test = pd.DataFrame(loss_epochs_test[index_i], index=['rmse', 'mae', 'precision', 'accuracy', 'balanced_accuracy', 'recall', 'f1'])
        df_loss_epochs_train = pd.DataFrame(loss_epochs_train[index_i], index=['bce', 'mse'])
        df_time_inference = pd.DataFrame(time_inference[index_i])
        df_loss_epochs_test_all = pd.concat([df_loss_epochs_test_all, df_loss_epochs_test], axis=0)
        df_loss_epochs_train_all = pd.concat([df_loss_epochs_train_all, df_loss_epochs_train], axis=0)
        df_time_inference_all = pd.concat([df_time_inference_all, df_time_inference], axis=0)
    df_loss_epochs_test_all = df_loss_epochs_test_all.T
    df_loss_epochs_train_all = df_loss_epochs_train_all.T
    # df_time_inference_all = df_time_inference_all.T
    df_loss_epochs_test_all.to_csv(os.path.join(save_path, "df_loss_epochs_test_all_{}.csv".format(network_type)))
    df_loss_epochs_train_all.to_csv(os.path.join(save_path, "df_loss_epochs_train_all_{}.csv".format(network_type)))
    df_time_inference_all.to_csv(os.path.join(save_path, "df_time_inference_all_{}.csv".format(network_type)))