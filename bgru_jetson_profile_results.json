[{"tag": "single_inference", "device": "cuda", "params": 7650, "model_size_mb": 0.03, "latency_ms": 76.02, "peak_mem_mb": 16.88, "active_mem_mb": 8.55, "power_w": null}, {"tag": "small_batch", "device": "cuda", "params": 7650, "model_size_mb": 0.03, "latency_ms": 8.68, "peak_mem_mb": 25.54, "active_mem_mb": 8.55, "power_w": null}, {"tag": "medium_batch", "device": "cuda", "params": 7650, "model_size_mb": 0.03, "latency_ms": 1.9, "peak_mem_mb": 25.74, "active_mem_mb": 8.55, "power_w": null}, {"tag": "large_batch", "device": "cuda", "params": 7650, "model_size_mb": 0.03, "latency_ms": 0.73, "peak_mem_mb": 26.13, "active_mem_mb": 8.55, "power_w": null}, {"tag": "long_sequence", "device": "cuda", "params": 7650, "model_size_mb": 0.03, "latency_ms": 0.87, "peak_mem_mb": 26.13, "active_mem_mb": 8.55, "power_w": null}, {"tag": "very_long_sequence", "device": "cuda", "params": 7650, "model_size_mb": 0.03, "latency_ms": 1.12, "peak_mem_mb": 26.13, "active_mem_mb": 8.55, "power_w": null}]