[{"tag": "single_inference", "device": "cuda", "params": 7650, "model_size_mb": 0.03, "latency_ms": 12.03, "peak_mem_mb": 16.88, "active_mem_mb": 8.55, "power_w": 28.09}, {"tag": "small_batch", "device": "cuda", "params": 7650, "model_size_mb": 0.03, "latency_ms": 2.53, "peak_mem_mb": 25.54, "active_mem_mb": 8.55, "power_w": 32.48}, {"tag": "medium_batch", "device": "cuda", "params": 7650, "model_size_mb": 0.03, "latency_ms": 1.34, "peak_mem_mb": 25.74, "active_mem_mb": 8.55, "power_w": 32.48}, {"tag": "large_batch", "device": "cuda", "params": 7650, "model_size_mb": 0.03, "latency_ms": 1.1, "peak_mem_mb": 26.13, "active_mem_mb": 8.55, "power_w": 36.85}, {"tag": "long_sequence", "device": "cuda", "params": 7650, "model_size_mb": 0.03, "latency_ms": 1.38, "peak_mem_mb": 25.51, "active_mem_mb": 8.55, "power_w": 36.85}, {"tag": "very_long_sequence", "device": "cuda", "params": 7650, "model_size_mb": 0.03, "latency_ms": 1.45, "peak_mem_mb": 25.67, "active_mem_mb": 8.55, "power_w": 41.03}]