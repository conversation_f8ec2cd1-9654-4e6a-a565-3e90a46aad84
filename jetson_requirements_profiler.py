import argparse, json, time, os, psutil, torch

def profile(model, sample_input, device='cuda', tag='default'):
    model = model.to(device).eval()
    torch.cuda.empty_cache()
    torch.cuda.reset_peak_memory_stats()

    # 1. 估算参数量 & 模型文件大小
    total_params = sum(p.numel() for p in model.parameters())
    model_size_mb = total_params * 4 / 1e6          # 4 bytes/param in FP32

    # 2. 推理延迟
    with torch.no_grad():
        torch.cuda.synchronize()
        t0 = time.perf_counter()
        _ = model(sample_input.to(device))
        torch.cuda.synchronize()
        latency_ms = (time.perf_counter() - t0) * 1000

    # 3. 峰值显存
    peak_mem_mb = torch.cuda.max_memory_allocated(device) / 1e6

    # 4. 活跃显存（exit 时）
    active_mem_mb = torch.cuda.memory_allocated(device) / 1e6

    # 5. GPU 功耗（若有 nvidia-smi，可用；Jetson 日后用 tegrastats）
    power_w = None
    try:
        import subprocess, re
        smi = subprocess.check_output(['nvidia-smi', '--query-gpu=power.draw', '--format=csv,noheader,nounits'])
        power_w = float(re.findall(r'\d+\.?\d*', smi.decode())[0])
    except Exception:
        pass

    return {
        "tag": tag,
        "device": device,
        "params": total_params,
        "model_size_mb": round(model_size_mb, 2),
        "latency_ms": round(latency_ms, 2),
        "peak_mem_mb": round(peak_mem_mb, 2),
        "active_mem_mb": round(active_mem_mb, 2),
        "power_w": power_w
    }

if __name__ == "__main__":
    """
    你只需要在自己的脚本里：
      1. import 这个文件
      2. 把你的 model 和 随机/真实的 sample_input 传进来
      3. 再调用 profile(model, sample_input, device='cuda', tag='my_LSTM_GRU')
    也可以直接当独立脚本运行，在这里构造 model 和 sample_input。
    """
    parser = argparse.ArgumentParser()
    parser.add_argument("--batch", type=int, default=1)
    parser.add_argument("--seq", type=int, default=100)
    args = parser.parse_args()

    ### ===== 下面示例请替换为你的模型 =====
    # 例：双层 LSTM, 隐层256，输出SOH回归
    from torch import nn
    class DemoNet(nn.Module):
        def __init__(self):
            super().__init__()
            self.lstm = nn.LSTM(input_size=1, hidden_size=256, num_layers=2, batch_first=True)
            self.fc = nn.Linear(256, 1)
        def forward(self, x):
            y, _ = self.lstm(x)
            return self.fc(y[:, -1, :])
    model = DemoNet()
    sample_input = torch.randn(args.batch, args.seq, 1)

    result = profile(model, sample_input, device='cuda', tag='demo')
    out_path = f"profile_results_{result['tag']}.json"
    with open(out_path, "w") as f:
        json.dump(result, f, indent=2)
    print(json.dumps(result, indent=2))
    print(f"Saved to {out_path}")