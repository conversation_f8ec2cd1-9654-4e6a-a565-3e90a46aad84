"""
BGRU模型简化性能分析脚本
直接使用原始的jetson_requirements_profiler
"""

import torch
from torch import nn
import json
import time
import argparse


def profile(model, sample_input, device='cuda', tag='default'):
    """
    从jetson_requirements_profiler.py复制的profile函数
    """
    model = model.to(device).eval()
    if device == 'cuda' and torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.reset_peak_memory_stats()

    # 1. 估算参数量 & 模型文件大小
    total_params = sum(p.numel() for p in model.parameters())
    model_size_mb = total_params * 4 / 1e6          # 4 bytes/param in FP32

    # 2. 推理延迟
    with torch.no_grad():
        if device == 'cuda' and torch.cuda.is_available():
            torch.cuda.synchronize()
        t0 = time.perf_counter()
        _ = model(sample_input.to(device))
        if device == 'cuda' and torch.cuda.is_available():
            torch.cuda.synchronize()
        latency_ms = (time.perf_counter() - t0) * 1000

    # 3. 峰值显存
    peak_mem_mb = 0
    active_mem_mb = 0
    if device == 'cuda' and torch.cuda.is_available():
        peak_mem_mb = torch.cuda.max_memory_allocated(device) / 1e6
        active_mem_mb = torch.cuda.memory_allocated(device) / 1e6

    # 5. GPU 功耗（若有 nvidia-smi，可用；Jetson 日后用 tegrastats）
    power_w = None
    try:
        import subprocess, re
        smi = subprocess.check_output(['nvidia-smi', '--query-gpu=power.draw', '--format=csv,noheader,nounits'])
        power_w = float(re.findall(r'\d+\.?\d*', smi.decode())[0])
    except Exception:
        pass

    return {
        "tag": tag,
        "device": device,
        "params": total_params,
        "model_size_mb": round(model_size_mb, 2),
        "latency_ms": round(latency_ms, 2),
        "peak_mem_mb": round(peak_mem_mb, 2),
        "active_mem_mb": round(active_mem_mb, 2),
        "power_w": power_w
    }


class My_net_lstm(nn.Module):
    """
    修改版的双向GRU网络，修复了device问题
    """
    def __init__(self, num_inputs, num_hiddens, num_outputs, num_layers=1, dropout=0.1, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)

        self.bgru1 = nn.GRU(num_inputs, num_hiddens, num_layers, dropout=dropout, bidirectional=True, batch_first=True)
        self.bgru2 = nn.GRU(num_hiddens*2, num_outputs, num_layers, dropout=dropout, bidirectional=True, batch_first=True)
        self.dropout = nn.Dropout(dropout)
        self.num_hiddens = num_hiddens
        self.num_layers = num_layers
        self.num_outputs = num_outputs

    def forward(self, X):
        # 修复：从输入张量获取device，而不是使用全局变量
        device = X.device
        h1 = torch.zeros((self.num_layers*2, X.shape[0], self.num_hiddens), device=device)
        h2 = torch.zeros((self.num_layers*2, X.shape[0], self.num_outputs), device=device)
        X = X.permute(0, 2, 1)
        output, state = self.bgru1(X, h1)
        output, state = self.bgru2(output, h2)
        output = self.dropout(state[-2:, :, :].permute(1, 0, 2).reshape(X.shape[0], -1))
        return output


class net_combine(nn.Module):
    """
    组合网络模型 - 多任务学习
    """
    def __init__(self, net1, net2, output_channels, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.nets = nn.ModuleList([net1, net2])
        self.linear1 = nn.Linear(output_channels, 1)  # 分类任务
        self.linear2 = nn.Linear(output_channels, 1)  # 回归任务

    def forward(self, X):
        X1 = self.nets[0](X)
        X2 = self.nets[1](X)
        return self.linear1(X1), self.linear2(X2)


def xavier_init_weights(m):
    """
    初始化网络的参数
    """
    if type(m) == nn.Linear or type(m) == nn.Conv1d:
        nn.init.xavier_uniform_(m.weight)
    if type(m) == nn.LSTM or type(m) == nn.GRU:
        for name, param in m.named_parameters():
            if name.startswith("weight"):
                nn.init.xavier_uniform_(param)


def create_bgru_model():
    """
    创建BGRU多任务模型
    """
    # 创建两个独立的GRU网络
    net1 = My_net_lstm(num_inputs=1, num_hiddens=8, num_outputs=16)
    net1.apply(xavier_init_weights)
    
    net2 = My_net_lstm(num_inputs=1, num_hiddens=8, num_outputs=16)
    net2.apply(xavier_init_weights)
    
    # 组合成多任务网络
    combined_net = net_combine(net1, net2, output_channels=32)
    
    return combined_net


if __name__ == "__main__":
    print("=" * 60)
    print("BGRU多任务模型 - Jetson性能分析")
    print("=" * 60)
    
    # 检查设备
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 创建模型
    model = create_bgru_model()
    print(f"模型参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 测试不同配置
    test_configs = [
        {"batch_size": 1, "seq_len": 30, "tag": "single_inference"},
        {"batch_size": 4, "seq_len": 30, "tag": "small_batch"},
        {"batch_size": 8, "seq_len": 30, "tag": "medium_batch"},
        {"batch_size": 16, "seq_len": 30, "tag": "large_batch"},
        {"batch_size": 1, "seq_len": 100, "tag": "long_sequence"},
        {"batch_size": 1, "seq_len": 200, "tag": "very_long_sequence"},
    ]
    
    results = []
    
    for config in test_configs:
        batch_size = config["batch_size"]
        seq_len = config["seq_len"]
        tag = config["tag"]
        
        print(f"\n测试配置: {tag} (batch={batch_size}, seq={seq_len})")
        
        # 创建样本输入 (batch_size, channels=1, seq_len)
        sample_input = torch.randn(batch_size, 1, seq_len)
        
        try:
            # 运行性能分析
            result = profile(model, sample_input, device=device, tag=tag)
            results.append(result)
            
            # 打印结果（按照原始格式）
            print(json.dumps(result, indent=2))
            
        except Exception as e:
            print(f"错误: {e}")
            continue
    
    # 保存所有结果
    output_file = "bgru_jetson_profile_complete.json"
    with open(output_file, "w", encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n所有结果已保存到: {output_file}")
