"""
BGRU模型性能分析脚本
用于评估双向GRU多任务模型在Jetson上的硬件消耗
"""

import torch
from torch import nn
import numpy as np
import json
import argparse
from jetson_requirements_profiler import profile


class My_net_lstm(nn.Module):
    """
    修改版的双向GRU网络，修复了device问题
    """
    def __init__(self, num_inputs, num_hiddens, num_outputs, num_layers=1, dropout=0.1, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)

        self.bgru1 = nn.GRU(num_inputs, num_hiddens, num_layers, dropout=dropout, bidirectional=True, batch_first=True)
        self.bgru2 = nn.GRU(num_hiddens*2, num_outputs, num_layers, dropout=dropout, bidirectional=True, batch_first=True)
        self.dropout = nn.Dropout(dropout)
        self.num_hiddens = num_hiddens
        self.num_layers = num_layers
        self.num_outputs = num_outputs

    def forward(self, X):
        # 修复：从输入张量获取device，而不是使用全局变量
        device = X.device
        h1 = torch.zeros((self.num_layers*2, X.shape[0], self.num_hiddens), device=device)
        h2 = torch.zeros((self.num_layers*2, X.shape[0], self.num_outputs), device=device)
        X = X.permute(0, 2, 1)
        output, state = self.bgru1(X, h1)
        output, state = self.bgru2(output, h2)
        output = self.dropout(state[-2:, :, :].permute(1, 0, 2).reshape(X.shape[0], -1))
        return output


class net_combine(nn.Module):
    """
    组合网络模型 - 多任务学习
    """
    def __init__(self, net1, net2, output_channels, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.nets = nn.ModuleList([net1, net2])
        self.linear1 = nn.Linear(output_channels, 1)  # 分类任务
        self.linear2 = nn.Linear(output_channels, 1)  # 回归任务

    def forward(self, X):
        X1 = self.nets[0](X)
        X2 = self.nets[1](X)
        return self.linear1(X1), self.linear2(X2)


def xavier_init_weights(m):
    """
    初始化网络的参数
    """
    if type(m) == nn.Linear or type(m) == nn.Conv1d:
        nn.init.xavier_uniform_(m.weight)
    if type(m) == nn.LSTM or type(m) == nn.GRU:
        for name, param in m.named_parameters():
            if name.startswith("weight"):
                nn.init.xavier_uniform_(param)


def create_bgru_model():
    """
    创建BGRU多任务模型
    """
    # 创建两个独立的GRU网络
    net1 = My_net_lstm(num_inputs=1, num_hiddens=8, num_outputs=16)
    net1.apply(xavier_init_weights)
    
    net2 = My_net_lstm(num_inputs=1, num_hiddens=8, num_outputs=16)
    net2.apply(xavier_init_weights)
    
    # 组合成多任务网络
    combined_net = net_combine(net1, net2, output_channels=32)
    
    return combined_net


def run_comprehensive_profile():
    """
    运行全面的性能分析
    """
    print("=" * 60)
    print("BGRU多任务模型 - Jetson性能分析")
    print("=" * 60)
    
    # 检查设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建模型
    model = create_bgru_model()
    print(f"模型结构:\n{model}")
    
    # 不同的输入配置进行测试
    test_configs = [
        {"batch_size": 1, "seq_len": 30, "tag": "single_inference"},
        {"batch_size": 4, "seq_len": 30, "tag": "small_batch"},
        {"batch_size": 8, "seq_len": 30, "tag": "medium_batch"},
        {"batch_size": 16, "seq_len": 30, "tag": "large_batch"},
        {"batch_size": 1, "seq_len": 100, "tag": "long_sequence"},
        {"batch_size": 1, "seq_len": 200, "tag": "very_long_sequence"},
    ]
    
    results = []
    
    for config in test_configs:
        batch_size = config["batch_size"]
        seq_len = config["seq_len"]
        tag = config["tag"]
        
        print(f"\n测试配置: {tag}")
        print(f"  批次大小: {batch_size}")
        print(f"  序列长度: {seq_len}")
        
        # 创建样本输入 (batch_size, channels=1, seq_len)
        sample_input = torch.randn(batch_size, 1, seq_len)
        
        try:
            # 运行性能分析
            result = profile(model, sample_input, device=str(device), tag=tag)
            results.append(result)
            
            # 打印结果
            print(f"  参数量: {result['params']:,}")
            print(f"  模型大小: {result['model_size_mb']} MB")
            print(f"  推理延迟: {result['latency_ms']} ms")
            print(f"  峰值显存: {result['peak_mem_mb']} MB")
            print(f"  活跃显存: {result['active_mem_mb']} MB")
            if result['power_w']:
                print(f"  功耗: {result['power_w']} W")
            
        except Exception as e:
            print(f"  错误: {e}")
            continue
    
    # 保存详细结果
    output_file = "bgru_jetson_profile_results.json"
    with open(output_file, "w", encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n详细结果已保存到: {output_file}")
    
    # 生成性能总结
    generate_performance_summary(results)
    
    return results


def generate_performance_summary(results):
    """
    生成性能总结报告
    """
    if not results:
        print("没有可用的性能数据")
        return
    
    print("\n" + "=" * 60)
    print("性能总结报告")
    print("=" * 60)
    
    # 基本模型信息
    base_result = results[0]
    print(f"模型参数量: {base_result['params']:,}")
    print(f"模型文件大小: {base_result['model_size_mb']} MB")
    
    # 延迟分析
    print(f"\n推理延迟分析:")
    for result in results:
        print(f"  {result['tag']}: {result['latency_ms']} ms")
    
    # 显存分析
    print(f"\n显存使用分析:")
    for result in results:
        print(f"  {result['tag']}: 峰值 {result['peak_mem_mb']} MB, 活跃 {result['active_mem_mb']} MB")
    
    # 吞吐量估算
    print(f"\n吞吐量估算 (样本/秒):")
    for result in results:
        if result['latency_ms'] > 0:
            # 从tag中提取batch_size
            if 'single' in result['tag']:
                batch_size = 1
            elif 'small' in result['tag']:
                batch_size = 4
            elif 'medium' in result['tag']:
                batch_size = 8
            elif 'large' in result['tag']:
                batch_size = 16
            else:
                batch_size = 1
            
            throughput = (batch_size * 1000) / result['latency_ms']
            print(f"  {result['tag']}: {throughput:.2f} 样本/秒")
    
    # Jetson部署建议
    print(f"\nJetson部署建议:")
    single_latency = next((r['latency_ms'] for r in results if r['tag'] == 'single_inference'), None)
    peak_memory = max(r['peak_mem_mb'] for r in results)
    
    if single_latency:
        if single_latency < 10:
            print("  ✓ 延迟表现优秀，适合实时应用")
        elif single_latency < 50:
            print("  ⚠ 延迟中等，适合准实时应用")
        else:
            print("  ✗ 延迟较高，可能需要优化")
    
    if peak_memory < 100:
        print("  ✓ 显存使用合理")
    elif peak_memory < 500:
        print("  ⚠ 显存使用中等")
    else:
        print("  ✗ 显存使用较高，注意内存限制")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="BGRU模型Jetson性能分析")
    parser.add_argument("--device", type=str, default="auto", choices=["auto", "cuda", "cpu"],
                       help="指定运行设备")
    
    args = parser.parse_args()
    
    # 运行性能分析
    results = run_comprehensive_profile()
