# BGRU 双向GRU神经网络项目介绍

## 项目概述

这是一个基于双向GRU（Bidirectional Gated Recurrent Unit）的深度学习项目，主要用于**电池健康状态（SOH）预测和异常检测**的多任务学习。该项目实现了一个端到端的机器学习流水线，包含数据预处理、模型训练、评估和结果保存等完整功能。

## 主要功能

### 1. 核心模型架构

- **My_net_lstm**: 双向GRU网络模型
  - 使用两层双向GRU结构
  - 支持dropout正则化
  - 适用于时序数据处理

- **net_combine**: 组合网络模型
  - 结合两个独立的GRU网络
  - 实现多任务学习（分类 + 回归）
  - 输出电池异常检测和SOH预测结果

### 2. 数据处理

- **TimeseriesDataset**: 自定义时序数据集类
  - 处理电池时序数据
  - 支持多标签输出（异常标签 + SOH值）
  - 自动数据类型转换和维度调整

- **数据标准化**: 使用StandardScaler进行特征标准化
- **交叉验证**: 采用5折分层交叉验证确保结果可靠性

### 3. 训练策略

- **学习率调度**: 
  - 实现余弦衰减学习率调度器（CosineScheduler）
  - 支持warmup机制
  - 动态调整学习率优化训练过程

- **损失函数**:
  - 分类任务：BCEWithLogitsLoss（带权重平衡）
  - 回归任务：MSELoss
  - 多任务损失加权组合

- **优化器**: Adam优化器
- **正则化**: Dropout + Xavier权重初始化

### 4. 评估指标

#### 分类任务指标：
- 准确率（Accuracy）
- 平衡准确率（Balanced Accuracy）
- 精确率（Precision）
- 召回率（Recall）
- F1分数

#### 回归任务指标：
- 均方根误差（RMSE）
- 平均绝对误差（MAE）

### 5. 实验管理

- **模型保存**: 自动保存最佳性能模型
- **结果记录**: 详细记录训练过程和评估结果
- **可视化**: 训练损失和测试指标的可视化展示
- **推理时间统计**: 记录模型推理性能

## 应用场景

这个项目主要应用于**电池管理系统（BMS）**中的：

1. **电池异常检测**: 识别电池是否处于异常状态
2. **SOH预测**: 预测电池的健康状态数值
3. **电池寿命评估**: 基于时序数据评估电池性能退化

## 技术特点

### 优势：
- **多任务学习**: 同时处理分类和回归任务，提高模型效率
- **双向GRU**: 能够捕获时序数据的前后依赖关系
- **鲁棒性**: 通过交叉验证和多种正则化技术提高模型泛化能力
- **可重现性**: 完整的随机种子设置确保实验可重现

### 创新点：
- 组合网络架构设计，实现参数共享的多任务学习
- 针对电池数据特点的损失函数权重设计
- 完整的实验管理和结果追踪系统

## 文件结构说明

```
bgru.py
├── 模型定义
│   ├── My_net_lstm (双向GRU基础网络)
│   └── net_combine (组合网络)
├── 数据处理
│   ├── TimeseriesDataset (数据集类)
│   └── 数据标准化和划分
├── 训练工具
│   ├── CosineScheduler (学习率调度)
│   ├── train_model (训练函数)
│   └── evaluate (评估函数)
├── 工具函数
│   ├── seed_torch (随机种子设置)
│   ├── xavier_init_weights (权重初始化)
│   └── balanced_accuracy_score (平衡准确率计算)
└── 主程序
    └── 完整的训练和评估流程
```

## 运行环境

- **深度学习框架**: PyTorch
- **数据处理**: NumPy, Pandas, Scikit-learn
- **可视化**: Matplotlib
- **性能分析**: thop (用于FLOPs计算)
- **硬件支持**: CUDA GPU加速

## 使用方法

1. 准备电池时序数据（pickle格式）
2. 调整超参数设置
3. 运行主程序进行训练和评估
4. 查看保存的模型和结果文件

这个项目为电池健康管理提供了一个完整的深度学习解决方案，具有良好的实用性和扩展性。
